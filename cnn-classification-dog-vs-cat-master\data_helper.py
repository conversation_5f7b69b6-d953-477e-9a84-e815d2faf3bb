# -*- coding: utf-8 -*-
"""
数据处理辅助模块
包含图像预处理、数据加载和批处理功能
"""

import os
import numpy as np
import tensorflow.compat.v1 as tf
import cv2

# 禁用TensorFlow 2.x行为，使用1.x兼容模式
tf.disable_v2_behavior()


def get_filenames_and_labels(dir_path, folder_names=['cat', 'dog'], shuffle=True):
    """
    获取文件路径和对应的标签

    参数:
        dir_path: 数据目录路径
        folder_names: 文件夹名称列表，默认为['cat', 'dog']
        shuffle: 是否随机打乱数据，默认为True

    返回:
        img_path_list_shuffled: 图像路径列表
        label_list_shuffled: 对应的标签列表
    """
    img_path_list = []
    label_list = []
    img_count = 0

    for folder_name in folder_names:
        filenames = os.listdir(os.path.join(dir_path, folder_name))
        for f in filenames:
            img_path_list.append(os.path.join(dir_path, folder_name, f))
            # 猫: [1,0], 狗: [0,1] (one-hot编码)
            label_list.append([1,0] if 'cat' in f else [0,1])
            img_count += 1

    img_path_list = np.array(img_path_list)
    label_list = np.array(label_list)

    if shuffle == True:
        # 随机打乱数据
        index = np.random.permutation(np.arange(0, img_count, 1))
        img_path_list_shuffled = img_path_list[index]
        label_list_shuffled = label_list[index]
    else:
        img_path_list_shuffled = img_path_list
        label_list_shuffled = label_list

    return img_path_list_shuffled, label_list_shuffled

def img_resize(img_path, img_height, img_width):
    """
    调整图像尺寸

    参数:
        img_path: 图像文件路径
        img_height: 目标高度
        img_width: 目标宽度

    返回:
        img_resized: 调整尺寸后的图像
    """
    img_src = cv2.imread(img_path)
    img_resized = cv2.resize(img_src, (img_width, img_height), interpolation=cv2.INTER_CUBIC)
    return img_resized

def rgb2gray(img_rgb):
    """
    将RGB图像转换为灰度图像并归一化

    参数:
        img_rgb: RGB图像数组

    返回:
        img_gray: 归一化的灰度图像，形状为(height, width, 1)
    """
    # 使用标准的RGB到灰度转换公式
    img_gray = np.dot(img_rgb[...,:3], [0.299, 0.587, 0.114])
    # 归一化到[0,1]范围
    img_gray = img_gray / 255.0
    # 重塑为(height, width, 1)
    return img_gray.reshape(img_rgb.shape[0], img_rgb.shape[1], 1)

def batch_iter(batch_size, num_epochs, img_path_list, label_list,
        img_height, img_width, shuffle=True):
    """
    为数据集生成批次迭代器

    参数:
        batch_size: 批次大小
        num_epochs: 训练轮数
        img_path_list: 图像路径列表
        label_list: 标签列表
        img_height: 图像高度
        img_width: 图像宽度
        shuffle: 是否随机打乱数据

    生成:
        img_list_shuffled: 批次图像数据
        label_list_shuffled: 批次标签数据
    """
    img_path_list = np.array(img_path_list)
    label_list = np.array(label_list)
    data_size = len(label_list)
    num_batches_per_epoch = int((data_size-1)/batch_size)+1

    for epoch in range(num_epochs):
        print(f'开始第 {epoch+1}/{num_epochs} 轮训练')

        if shuffle:
            # 随机打乱数据
            shuffle_indices = np.random.permutation(np.arange(data_size))
            img_path_list_shuffled = img_path_list[shuffle_indices]
            label_list_shuffled = label_list[shuffle_indices]
        else:
            img_path_list_shuffled = img_path_list
            label_list_shuffled = label_list

        for batch_num in range(num_batches_per_epoch):
            start_index = batch_num*batch_size
            end_index = min((batch_num+1)*batch_size, data_size)
            img_list_shuffled = []

            # 处理当前批次的图像
            for i in range(start_index, end_index):
                # 调整图像尺寸
                img_data = img_resize(img_path=img_path_list_shuffled[i],
                                    img_height=img_height, img_width=img_width)
                # 转换为灰度图像并归一化
                img_data = rgb2gray(img_data)
                img_list_shuffled.append(img_data)

            img_list_shuffled = np.array(img_list_shuffled)
            yield img_list_shuffled, label_list_shuffled[start_index:end_index]

def generate_arrays_from_file(batch_size, img_path_list, label_list,
        img_height, img_width, shuffle=True):
    """
    为Keras模型生成批次数据迭代器（用于VGG16迁移学习）

    参数:
        batch_size: 批次大小
        img_path_list: 图像路径列表
        label_list: 标签列表
        img_height: 图像高度
        img_width: 图像宽度
        shuffle: 是否随机打乱数据

    生成:
        包含输入和输出的字典格式数据，适用于Keras模型
    """
    img_path_list = np.array(img_path_list)
    label_list = np.array(label_list)
    data_size = len(label_list)
    num_batches_per_epoch = int((data_size-1)/batch_size)+1

    while True:  # Keras需要无限循环的生成器
        if shuffle:
            # 随机打乱数据
            shuffle_indices = np.random.permutation(np.arange(data_size))
            img_path_list_shuffled = img_path_list[shuffle_indices]
            label_list_shuffled = label_list[shuffle_indices]
        else:
            img_path_list_shuffled = img_path_list
            label_list_shuffled = label_list

        for batch_num in range(num_batches_per_epoch):
            start_index = batch_num*batch_size
            end_index = min((batch_num+1)*batch_size, data_size)
            img_list_shuffled = []

            # 处理当前批次的图像（保持RGB格式用于VGG16）
            for i in range(start_index, end_index):
                img_data = img_resize(img_path=img_path_list_shuffled[i],
                                    img_height=img_height, img_width=img_width)
                # VGG16需要RGB图像，所以不转换为灰度
                img_data = img_data / 255.0  # 归一化到[0,1]
                img_list_shuffled.append(img_data)

            img_list_shuffled = np.array(img_list_shuffled)
            # 返回Keras期望的字典格式
            yield ({'input_1': img_list_shuffled},
                   {'output': label_list_shuffled[start_index:end_index]})
