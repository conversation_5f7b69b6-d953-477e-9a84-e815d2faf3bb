# -*- coding: utf-8 -*-
"""
基于VGG16的猫狗图像分类 - 迁移学习训练脚本
使用预训练的VGG16模型进行猫狗图像分类
"""

import sys
import gflags
import keras
import matplotlib.pyplot as plt

### 参数配置 ###
# ===============================================
FLAGS = gflags.FLAGS

# 数据加载参数
gflags.DEFINE_string('train_data_dir', './inputs/train/',
                     '训练数据目录路径')
gflags.DEFINE_string('dev_data_dir', './inputs/dev/',
                     '验证数据目录路径')
# gflags.DEFINE_float('dev_sample_percentage', 0.02, '用于验证的训练数据百分比')

# 模型参数
gflags.DEFINE_integer('img_height', 224,
                      '训练图像的高度 (默认: 224)')
gflags.DEFINE_integer('img_width', 224,
                      '训练图像的宽度 (默认: 224)')
gflags.DEFINE_integer(
    'img_channels', 3,
    '训练图像的通道数 (默认: 3)')
gflags.DEFINE_float('dropout_keep_prob', 0.7,
                    'Dropout保留概率 (默认: 0.7)')

# 训练参数
gflags.DEFINE_float('learning_rate', 0.001, '学习率')
gflags.DEFINE_integer('batch_size', 32, '每个训练步骤的批次大小')
gflags.DEFINE_integer('num_epochs', 10,
                      '训练轮数 (默认: 10)')

FLAGS(sys.argv)
# 显示参数配置
print('\n参数配置:')
print('================================')
for attr, value in FLAGS.flag_values_dict().items():
    print('{0}: {1}'.format(attr.lower(), value))
print('================================\n\n')

### use the pre-trained model
# create the base pre-trained model
base_model = keras.applications.VGG16(
    weights='imagenet',
    include_top=False,
    input_shape=(FLAGS.img_height, FLAGS.img_width, FLAGS.img_channels))

# add a global spatial average pooling layer
add_model = keras.Sequential(name='additional_layers')
add_model.add(keras.layers.Flatten(input_shape=base_model.output_shape[1:]))
add_model.add(keras.layers.Dense(128, activation='relu'))
add_model.add(keras.layers.Dense(2, activation='softmax'))

model = keras.models.Model(
    inputs=base_model.input, outputs=add_model(base_model.output))

# freeze all VGG16 layers
for layer in model.layers[:-1]:
    layer.trainable = False

model.compile(
    loss='categorical_crossentropy',
    optimizer=keras.optimizers.SGD(learning_rate=FLAGS.learning_rate, momentum=0.9),
    metrics=['accuracy'])

model.summary()

# 数据增强和预处理
train_datagen = keras.preprocessing.image.ImageDataGenerator(
    rescale=1. / 255,        # 像素值归一化到[0,1]
    shear_range=0.2,         # 剪切变换
    zoom_range=0.2,          # 缩放变换
    horizontal_flip=True)    # 水平翻转

validation_datagen = keras.preprocessing.image.ImageDataGenerator(rescale=1./255)

# 训练数据生成器
train_generator = train_datagen.flow_from_directory(
    directory=FLAGS.train_data_dir,
    target_size=(FLAGS.img_height, FLAGS.img_width),
    batch_size=FLAGS.batch_size,
    class_mode='categorical',  # 分类模式
    seed=272)

# 验证数据生成器
validation_generator = validation_datagen.flow_from_directory(
    directory=FLAGS.dev_data_dir,
    target_size=(FLAGS.img_height, FLAGS.img_width),
    batch_size=FLAGS.batch_size,
    class_mode='categorical')

# 开始训练模型
print("开始训练模型...")
history = model.fit(
    train_generator,
    steps_per_epoch=train_generator.n // FLAGS.batch_size,  # 每轮的步数
    epochs=FLAGS.num_epochs,                                # 训练轮数
    validation_data=validation_generator,                   # 验证数据
    verbose=1,                                              # 显示训练过程
    callbacks=[
        keras.callbacks.ModelCheckpoint(
            './log/VGG16-transfer-learning.h5',     # 模型保存路径
            monitor='val_loss',                      # 监控验证损失
            save_best_only=True,                     # 只保存最佳模型
            verbose=1)                               # 显示保存信息
    ])

# 绘制训练历史 - 准确率
plt.figure(figsize=(12, 4))

plt.subplot(1, 2, 1)
# 在新版本Keras中，'acc'改为'accuracy'
acc_key = 'accuracy' if 'accuracy' in history.history else 'acc'
val_acc_key = 'val_accuracy' if 'val_accuracy' in history.history else 'val_acc'
plt.plot(history.history[acc_key])
plt.plot(history.history[val_acc_key])
plt.title('模型准确率')
plt.ylabel('准确率')
plt.xlabel('训练轮数')
plt.legend(['训练', '验证'], loc='upper left')

# 绘制训练历史 - 损失
plt.subplot(1, 2, 2)
plt.plot(history.history['loss'])
plt.plot(history.history['val_loss'])
plt.title('模型损失')
plt.ylabel('损失')
plt.xlabel('训练轮数')
plt.legend(['训练', '验证'], loc='upper left')

plt.tight_layout()
plt.show()

# 输出最终训练结果
print("训练完成！")
print("训练损失: {:.4f} / 验证损失: {:.4f}".format(
    history.history['loss'][-1], history.history['val_loss'][-1]))
print("训练准确率: {:.2f}% / 验证准确率: {:.2f}%".format(
    100 * history.history[acc_key][-1], 100 * history.history[val_acc_key][-1]))

print("\n模型已保存到: ./log/VGG16-transfer-learning.h5")
