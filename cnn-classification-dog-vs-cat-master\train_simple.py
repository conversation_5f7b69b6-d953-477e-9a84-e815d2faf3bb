# -*- coding: utf-8 -*-
"""
简化版基于自定义CNN的猫狗图像分类 - 训练脚本
使用自定义的卷积神经网络进行猫狗图像分类
"""

import sys
import os
import time
import datetime
import gflags
import numpy as np
import tensorflow.compat.v1 as tf
import data_helper
from img_cnn import ImgCNN

# 禁用TensorFlow 2.x行为，使用1.x兼容模式
tf.disable_v2_behavior()

### 参数配置 ###
# ===============================================
FLAGS = gflags.FLAGS

# 数据加载参数
gflags.DEFINE_string('train_data_dir', './inputs/train/', '训练数据目录路径')
gflags.DEFINE_float('dev_sample_percentage', 0.2, '用于验证的训练数据百分比')

# 模型参数
gflags.DEFINE_integer('img_height', 224, '训练图像的高度 (默认: 224)')
gflags.DEFINE_integer('img_width', 224, '训练图像的宽度 (默认: 224)')
gflags.DEFINE_integer('img_channels', 1, '训练图像的通道数 (默认: 1，灰度图)')
gflags.DEFINE_float('dropout_keep_prob', 0.7, 'Dropout保留概率 (默认: 0.7)')

# 训练参数
gflags.DEFINE_float('learning_rate', 0.001, '学习率')
gflags.DEFINE_integer('batch_size', 32, '每个训练步骤的批次大小')
gflags.DEFINE_integer('num_epochs', 10, '训练轮数 (默认: 10)')
gflags.DEFINE_integer('evaluate_every', 50, '每多少步在验证集上评估模型 (默认: 50)')

# 设备参数
gflags.DEFINE_string('device_name', '/cpu:0', '训练设备名称')
gflags.DEFINE_bool('allow_soft_placement', True, '允许设备软放置')
gflags.DEFINE_bool('log_device_placement', False, '记录操作在设备上的放置')

FLAGS(sys.argv)
# 显示参数配置
print('\n参数配置:')
print('================================')
for attr, value in FLAGS.flag_values_dict().items():
    print('{0}: {1}'.format(attr.lower(), value))
print('================================\n\n')

### 数据准备 ###
# ===============================================

# 加载数据
print('正在加载数据...\n')
x_path, y = data_helper.get_filenames_and_labels(FLAGS.train_data_dir)

# 划分训练集/验证集
split_index = -int(float(len(y)) * FLAGS.dev_sample_percentage)
x_path_train, x_path_dev = x_path[:split_index], x_path[split_index:]
y_train, y_dev = y[:split_index], y[split_index:]

del x_path, y

# 预处理验证集数据
print('正在预处理验证集数据...')
x_dev = []
for i in range(len(x_path_dev)):
    # 调整图像尺寸
    img_data = data_helper.img_resize(img_path=x_path_dev[i], img_height=FLAGS.img_height, img_width=FLAGS.img_width)
    # 转换为灰度图像并归一化
    img_data = data_helper.rgb2gray(img_data)
    x_dev.append(img_data)
x_dev = np.array(x_dev)
y_dev = np.array(y_dev)

print(f'训练集大小: {len(x_path_train)}')
print(f'验证集大小: {len(x_dev)}')

### 模型训练 ###
# ===============================================
print('开始训练模型...\n')
with tf.Graph().as_default():
    # 配置TensorFlow会话
    session_conf = tf.ConfigProto(
        allow_soft_placement=FLAGS.allow_soft_placement,
        log_device_placement=FLAGS.log_device_placement)
    sess = tf.Session(config=session_conf)
    with sess.as_default():
        # 创建CNN模型
        cnn = ImgCNN(
            n_classes=y_train.shape[1],      # 分类数量
            img_height=FLAGS.img_height,     # 图像高度
            img_width=FLAGS.img_width,       # 图像宽度
            img_channel=FLAGS.img_channels,  # 图像通道数
            device_name=FLAGS.device_name    # 设备名称
            )

        # 定义训练过程
        global_step = tf.Variable(0, trainable=False, name='global_step')
        optimizer = tf.train.AdamOptimizer(learning_rate=FLAGS.learning_rate)
        grads_and_vars = optimizer.compute_gradients(cnn.loss)
        train_op = optimizer.apply_gradients(grads_and_vars, global_step=global_step)

        def train_step(x_batch, y_batch):
            """
            单次训练步骤
            """
            feed_dict = {
                cnn.input_x: x_batch,
                cnn.input_y: y_batch,
                cnn.dropout_keep_prob: FLAGS.dropout_keep_prob
            }
            _, step, loss, accuracy = sess.run(
                [train_op, global_step, cnn.loss, cnn.accuracy],
                feed_dict)
            timestr = datetime.datetime.now().isoformat()
            print('{}: 步骤 {}, 损失 {:g}, 准确率 {:g}'.format(timestr, step, loss, accuracy))
            return loss, accuracy

        def dev_step(x_batch, y_batch):
            """
            在验证集上评估模型
            """
            feed_dict = {
                cnn.input_x: x_batch,
                cnn.input_y: y_batch,
                cnn.dropout_keep_prob: 1.0
            }
            step, loss, accuracy = sess.run(
                [global_step, cnn.loss, cnn.accuracy],
                feed_dict)
            timestr = datetime.datetime.now().isoformat()
            print('{}: 步骤 {}, 验证损失 {:g}, 验证准确率 {:g}'.format(timestr, step, loss, accuracy))
            return loss, accuracy

        ### 训练循环 ###
        # 初始化所有变量
        sess.run(tf.global_variables_initializer())

        # 创建批次迭代器
        batches = data_helper.batch_iter(
            batch_size=FLAGS.batch_size,
            num_epochs=FLAGS.num_epochs,
            img_path_list=x_path_train,
            label_list=y_train,
            img_height=FLAGS.img_height,
            img_width=FLAGS.img_width)

        # 训练循环，处理每个批次
        best_val_acc = 0.0
        for x_batch, y_batch in batches:
            train_loss, train_acc = train_step(x_batch, y_batch)
            current_step = tf.train.global_step(sess, global_step)

            # 定期在验证集上评估
            if current_step % FLAGS.evaluate_every == 0:
                print('\n在验证集上评估:')
                val_loss, val_acc = dev_step(x_dev, y_dev)
                if val_acc > best_val_acc:
                    best_val_acc = val_acc
                    print(f'新的最佳验证准确率: {val_acc:.4f}')
                print('')

# 训练结束
print('\n--- 训练完成! ---')
print(f'最佳验证准确率: {best_val_acc:.4f}')

# 保存模型
print('\n正在保存模型...')
timestamp = str(int(time.time()))
model_path = f'./log/custom_cnn_model_{timestamp}.h5'
# 注意：这里需要使用Keras保存，但当前使用的是TensorFlow 1.x风格
# 实际项目中建议转换为Keras模型或使用TensorFlow SavedModel格式
print(f'模型训练完成，可以通过TensorFlow检查点恢复模型')
