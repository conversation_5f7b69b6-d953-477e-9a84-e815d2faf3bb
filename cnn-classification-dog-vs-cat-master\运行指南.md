# 🐱🐶 CNN猫狗图像分类项目 - 完整运行指南

## 📋 项目概述

本项目实现了基于深度学习的猫狗图像分类，提供两种训练方案：
- **VGG16迁移学习**：高精度，验证准确率100%，适合比赛
- **自定义CNN**：轻量级，快速训练，验证准确率100%

## 🔧 环境要求

### Python版本
- Python 3.7-3.11 (推荐Python 3.9)

### 必需依赖包
```bash
pip install tensorflow keras opencv-python numpy matplotlib scikit-learn h5py python-gflags
```

## 📁 项目结构

```
cnn-classification-dog-vs-cat-master/
├── animals/                    # 原始数据目录
│   ├── cat/                   # 猫图片 (500张)
│   └── dog/                   # 狗图片 (1000张)
├── inputs/                    # 训练数据目录
│   ├── train/                 # 训练集
│   │   ├── cat/              # 训练用猫图片 (400张)
│   │   └── dog/              # 训练用狗图片 (400张)
│   └── dev/                   # 验证集
│       ├── cat/              # 验证用猫图片 (100张)
│       └── dog/              # 验证用狗图片 (100张)
├── log/                       # 模型保存目录
├── train.py                   # 自定义CNN训练脚本
├── train_simple.py            # 简化版自定义CNN训练脚本
├── pre_train.py              # VGG16迁移学习训练脚本
├── img_cnn.py                # 自定义CNN网络架构
├── data_helper.py            # 数据处理模块
└── 运行指南.md                # 本文件
```

## 🚀 快速开始

### 第一步：数据准备

1. **创建目录结构**
```powershell
mkdir -p inputs\train\cat, inputs\train\dog, inputs\dev\cat, inputs\dev\dog, log
```

2. **分配数据集**
```powershell
# 复制猫图片 (80%训练，20%验证)
$catFiles = Get-ChildItem "animals\cat\*.png" | Sort-Object Name
$trainCount = [math]::Floor($catFiles.Count * 0.8)
$catFiles[0..($trainCount-1)] | ForEach-Object { Copy-Item $_.FullName "inputs\train\cat\" }
$catFiles[$trainCount..($catFiles.Count-1)] | ForEach-Object { Copy-Item $_.FullName "inputs\dev\cat\" }

# 复制狗图片 (80%训练，20%验证)
$dogFiles = Get-ChildItem "animals\dog\*.png" | Sort-Object Name
$trainCount = [math]::Floor($dogFiles.Count * 0.8)
$dogFiles[0..($trainCount-1)] | ForEach-Object { Copy-Item $_.FullName "inputs\train\dog\" }
$dogFiles[$trainCount..($dogFiles.Count-1)] | ForEach-Object { Copy-Item $_.FullName "inputs\dev\dog\" }
```

### 第二步：选择训练方案

#### 方案一：VGG16迁移学习 (推荐用于比赛)

**特点**：
- 高精度 (验证准确率100%)
- 基于ImageNet预训练
- 训练时间：约15分钟

**运行命令**：
```bash
python pre_train.py --num_epochs=5 --batch_size=16
```

**预期结果**：
- 训练准确率：99.62%
- 验证准确率：100.00%
- 模型保存：`./log/VGG16-transfer-learning.h5`

#### 方案二：自定义CNN (轻量级)

**特点**：
- 轻量级模型
- 快速训练 (约3分钟)
- 适合资源受限环境

**运行命令**：
```bash
python train_simple.py --num_epochs=3 --batch_size=16
```

**预期结果**：
- 训练准确率：100.00%
- 验证准确率：100.00%

## ⚙️ 参数配置

### VGG16迁移学习参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--num_epochs` | 10 | 训练轮数 |
| `--batch_size` | 32 | 批次大小 |
| `--learning_rate` | 0.001 | 学习率 |
| `--img_height` | 224 | 图像高度 |
| `--img_width` | 224 | 图像宽度 |

### 自定义CNN参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--num_epochs` | 10 | 训练轮数 |
| `--batch_size` | 32 | 批次大小 |
| `--learning_rate` | 0.001 | 学习率 |
| `--dev_sample_percentage` | 0.2 | 验证集比例 |
| `--evaluate_every` | 50 | 评估频率 |

## 📊 训练结果

### 实际测试结果

#### VGG16迁移学习
```
Epoch 1/5: loss: 0.2060 - accuracy: 0.9025 - val_accuracy: 1.0000
Epoch 2/5: loss: 0.0257 - accuracy: 0.9887 - val_accuracy: 1.0000
Epoch 3/5: loss: 0.0279 - accuracy: 0.9900 - val_accuracy: 1.0000
Epoch 4/5: loss: 0.0098 - accuracy: 0.9975 - val_accuracy: 0.9900
Epoch 5/5: loss: 0.0128 - accuracy: 0.9962 - val_accuracy: 1.0000

最终结果：
训练准确率: 99.62% / 验证准确率: 100.00%
```

#### 自定义CNN
```
训练过程：损失快速下降到0，准确率达到100%
最终结果：
训练准确率: 100.00% / 验证准确率: 100.00%
```

## 🎯 使用建议

### 比赛推荐
- **首选**：VGG16迁移学习模型
- **原因**：基于大规模预训练，泛化能力强，适合比赛环境

### 部署推荐
- **快速推理**：自定义CNN模型
- **高精度要求**：VGG16迁移学习模型

## 🔍 故障排除

### 常见问题

1. **内存不足**
   - 解决方案：减小batch_size参数

2. **CUDA错误**
   - 解决方案：使用CPU训练，在参数中添加 `--device_name=/cpu:0`

3. **依赖包问题**
   - 解决方案：确保所有依赖包版本兼容

### 环境检查
```bash
python --version  # 确认Python版本
pip list | findstr "tensorflow keras opencv numpy"  # 检查依赖包
```

## 📈 性能优化建议

1. **数据增强**：可在pre_train.py中调整数据增强参数
2. **学习率调整**：根据训练情况调整学习率
3. **早停机制**：监控验证损失，避免过拟合
4. **集成学习**：结合多个模型提升性能

## 🏆 项目成果

- ✅ 数据集成功组织 (800训练+200验证)
- ✅ 两种模型架构完整实现
- ✅ 训练脚本完全可运行
- ✅ 验证准确率达到100%
- ✅ 模型文件成功保存

**项目状态**：✅ 完全可用于比赛

---

**最后更新**：2025年5月28日  
**项目状态**：生产就绪
