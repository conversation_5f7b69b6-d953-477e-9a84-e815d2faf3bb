#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据准备脚本
用于将原始数据分割为训练集和验证集
"""

import os
import shutil
import random
from pathlib import Path

def create_directory_structure():
    """创建训练和验证数据目录结构"""
    directories = [
        "inputs/train/cat",
        "inputs/train/dog", 
        "inputs/dev/cat",
        "inputs/dev/dog"
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {dir_path}")

def split_dataset(source_dir, train_dir, dev_dir, split_ratio=0.8):
    """分割数据集为训练集和验证集"""
    # 获取所有图片文件
    image_files = [f for f in os.listdir(source_dir) if f.endswith('.png')]
    random.shuffle(image_files)
    
    # 计算分割点
    split_point = int(len(image_files) * split_ratio)
    train_files = image_files[:split_point]
    dev_files = image_files[split_point:]
    
    # 复制训练集文件
    for file in train_files:
        src = os.path.join(source_dir, file)
        dst = os.path.join(train_dir, file)
        shutil.copy2(src, dst)
    
    # 复制验证集文件
    for file in dev_files:
        src = os.path.join(source_dir, file)
        dst = os.path.join(dev_dir, file)
        shutil.copy2(src, dst)
    
    return len(train_files), len(dev_files)

def main():
    """主函数"""
    print("开始准备数据集...")
    
    # 创建目录结构
    create_directory_structure()
    
    # 分割猫图片
    cat_train_count, cat_dev_count = split_dataset(
        "animals/cat",
        "inputs/train/cat", 
        "inputs/dev/cat"
    )
    
    # 分割狗图片
    dog_train_count, dog_dev_count = split_dataset(
        "animals/dog",
        "inputs/train/dog",
        "inputs/dev/dog"
    )
    
    print("\n" + "="*50)
    print("数据集分割完成！")
    print("="*50)
    print(f"训练集 - 猫: {cat_train_count}, 狗: {dog_train_count}")
    print(f"验证集 - 猫: {cat_dev_count}, 狗: {dog_dev_count}")
    print(f"总计 - 训练: {cat_train_count + dog_train_count}, 验证: {cat_dev_count + dog_dev_count}")
    
    # 验证目录结构
    print("\n目录结构验证:")
    for root, dirs, files in os.walk("inputs"):
        level = root.replace("inputs", "").count(os.sep)
        indent = " " * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = " " * 2 * (level + 1)
        print(f"{subindent}文件数量: {len(files)}")

if __name__ == "__main__":
    main()
